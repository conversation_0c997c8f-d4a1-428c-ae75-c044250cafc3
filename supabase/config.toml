# Supabase Configuration for Blog Website
# Project ID: cgmlpbxwmqynmshecaqn

project_id = "cgmlpbxwmqynmshecaqn"

[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[auth]
enabled = true
port = 54322
site_url = "http://localhost:3000"
additional_redirect_urls = ["https://localhost:3000"]
jwt_expiry = 3600
enable_signup = true
enable_confirmations = false

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = false

[auth.sms]
enable_signup = false
enable_confirmations = false

[auth.external.apple]
enabled = false

[auth.external.azure]
enabled = false

[auth.external.bitbucket]
enabled = false

[auth.external.discord]
enabled = false

[auth.external.facebook]
enabled = false

[auth.external.github]
enabled = false

[auth.external.gitlab]
enabled = false

[auth.external.google]
enabled = false

[auth.external.keycloak]
enabled = false

[auth.external.linkedin]
enabled = false

[auth.external.notion]
enabled = false

[auth.external.twitch]
enabled = false

[auth.external.twitter]
enabled = false

[auth.external.slack]
enabled = false

[auth.external.spotify]
enabled = false

[auth.external.workos]
enabled = false

[auth.external.zoom]
enabled = false

[db]
port = 54322
shadow_port = 54320
major_version = 15

[realtime]
enabled = true
port = 54323

[studio]
enabled = true
port = 54323

[inbucket]
enabled = true
port = 54324

[storage]
enabled = true
port = 54325
file_size_limit = "50MiB"
image_transformation = true

[edge_runtime]
enabled = true
port = 54326

[analytics]
enabled = false

[functions]
enabled = true
