-- Migration: Database Functions
-- Created: 2025-07-30
-- Description: Creates utility functions for blog functionality

-- Function to automatically create user profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, full_name)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create user profile on auth.users insert
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to increment post view count
CREATE OR REPLACE FUNCTION public.increment_post_views(post_uuid UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE public.posts 
  SET view_count = view_count + 1 
  WHERE id = post_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get post with author and category details
CREATE OR REPLACE FUNCTION public.get_post_with_details(post_slug TEXT)
RETURNS TABLE (
  id UUID,
  title TEXT,
  slug TEXT,
  excerpt TEXT,
  content TEXT,
  featured_image TEXT,
  status TEXT,
  tags TEXT[],
  meta_title TEXT,
  meta_description TEXT,
  reading_time INTEGER,
  view_count INTEGER,
  published_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  author_name TEXT,
  author_username TEXT,
  author_avatar TEXT,
  category_name TEXT,
  category_slug TEXT,
  category_color TEXT,
  likes_count BIGINT,
  comments_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.title,
    p.slug,
    p.excerpt,
    p.content,
    p.featured_image,
    p.status,
    p.tags,
    p.meta_title,
    p.meta_description,
    p.reading_time,
    p.view_count,
    p.published_at,
    p.created_at,
    p.updated_at,
    u.full_name as author_name,
    u.username as author_username,
    u.avatar_url as author_avatar,
    c.name as category_name,
    c.slug as category_slug,
    c.color as category_color,
    COALESCE(l.likes_count, 0) as likes_count,
    COALESCE(cm.comments_count, 0) as comments_count
  FROM public.posts p
  LEFT JOIN public.users u ON p.author_id = u.id
  LEFT JOIN public.categories c ON p.category_id = c.id
  LEFT JOIN (
    SELECT post_id, COUNT(*) as likes_count
    FROM public.likes
    GROUP BY post_id
  ) l ON p.id = l.post_id
  LEFT JOIN (
    SELECT post_id, COUNT(*) as comments_count
    FROM public.comments
    WHERE status = 'approved'
    GROUP BY post_id
  ) cm ON p.id = cm.post_id
  WHERE p.slug = post_slug;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get popular posts
CREATE OR REPLACE FUNCTION public.get_popular_posts(limit_count INTEGER DEFAULT 5)
RETURNS TABLE (
  id UUID,
  title TEXT,
  slug TEXT,
  excerpt TEXT,
  featured_image TEXT,
  view_count INTEGER,
  likes_count BIGINT,
  published_at TIMESTAMPTZ,
  author_name TEXT,
  category_name TEXT,
  category_color TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.title,
    p.slug,
    p.excerpt,
    p.featured_image,
    p.view_count,
    COALESCE(l.likes_count, 0) as likes_count,
    p.published_at,
    u.full_name as author_name,
    c.name as category_name,
    c.color as category_color
  FROM public.posts p
  LEFT JOIN public.users u ON p.author_id = u.id
  LEFT JOIN public.categories c ON p.category_id = c.id
  LEFT JOIN (
    SELECT post_id, COUNT(*) as likes_count
    FROM public.likes
    GROUP BY post_id
  ) l ON p.id = l.post_id
  WHERE p.status = 'published'
  ORDER BY p.view_count DESC, l.likes_count DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to search posts
CREATE OR REPLACE FUNCTION public.search_posts(search_term TEXT, limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
  id UUID,
  title TEXT,
  slug TEXT,
  excerpt TEXT,
  featured_image TEXT,
  published_at TIMESTAMPTZ,
  author_name TEXT,
  category_name TEXT,
  rank REAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.title,
    p.slug,
    p.excerpt,
    p.featured_image,
    p.published_at,
    u.full_name as author_name,
    c.name as category_name,
    ts_rank(
      to_tsvector('english', p.title || ' ' || p.excerpt || ' ' || p.content),
      plainto_tsquery('english', search_term)
    ) as rank
  FROM public.posts p
  LEFT JOIN public.users u ON p.author_id = u.id
  LEFT JOIN public.categories c ON p.category_id = c.id
  WHERE p.status = 'published'
    AND (
      to_tsvector('english', p.title || ' ' || p.excerpt || ' ' || p.content) 
      @@ plainto_tsquery('english', search_term)
    )
  ORDER BY rank DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
