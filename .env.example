# Supabase Configuration
# Copy this file to .env.local and fill in your values

# Supabase Project URL
NEXT_PUBLIC_SUPABASE_URL=https://cgmlpbxwmqynmshecaqn.supabase.co

# Supabase Anon Key (Public)
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnbWxwYnh3bXF5bm1zaGVjYXFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM4ODYzOTIsImV4cCI6MjA2OTQ2MjM5Mn0.PJambQA-fPcmJPjsLuLJrTtYZXqOx7wzuslJjDjzo_A

# Supabase Service Role Key (Private - Server-side only)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnbWxwYnh3bXF5bm1zaGVjYXFuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1Mzg4NjM5MiwiZXhwIjoyMDY5NDYyMzkyfQ.v1LM4fFcfeMRGwdETiUaWGWM6lEVD3BkpmFTI3sp1WM

# Database URL (for direct connections)
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.cgmlpbxwmqynmshecaqn.supabase.co:5432/postgres

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-here

# Optional: File Upload Configuration
NEXT_PUBLIC_STORAGE_BUCKET=blog-images

# Optional: Email Configuration (for notifications)
SMTP_HOST=
SMTP_PORT=
SMTP_USER=
SMTP_PASS=

# Optional: Analytics
GOOGLE_ANALYTICS_ID=
