// Enhanced Multiple Shayari Post Application

// Application data from JSON
const applicationData = {
    post: {
        title: "दिल के जज़्बात - हृदय की भावनाएं",
        author: "राहुल शर्मा",
        date: "30 जुलाई 2025",
        category: "प्रेम शायरी",
        totalShayari: 4,
        views: 2847,
        likes: 156
    },
    shayariCollection: [
        {
            id: 1,
            text: "तुम्हारी यादों में खो जाना\nये दिल की आदत हो गई है\nतुम्हारे बिना जी पाना\nअब मुश्किल सी बात हो गई है",
            author: "राहुल शर्मा",
            theme: "love",
            likes: 89,
            views: 432,
            shareCount: 67,
            isLiked: false
        },
        {
            id: 2,
            text: "टूटे दिल की आवाज़ सुनो\nख़ामोशी में छुपे ग़म को समझो\nहर आंसू में एक कहानी है\nबस मोहब्बत की नादानी है",
            author: "राहुल शर्मा", 
            theme: "sad",
            likes: 124,
            views: 567,
            shareCount: 89,
            isLiked: false
        },
        {
            id: 3,
            text: "हौसला रख, सफर अभी बाकी है\nमंजिल तक का रास्ता अभी बाकी है\nहार मत मान, जीतना तेरा है\nतेरी जिंदगी का खेल अभी बाकी है",
            author: "राहुल शर्मा",
            theme: "motivational",
            likes: 201,
            views: 756,
            shareCount: 134,
            isLiked: false
        },
        {
            id: 4,
            text: "दोस्ती है तो सब कुछ है\nयारों के साथ जो है सो खुशी है\nजिंदगी के हर मोड़ पर\nसच्चे दोस्त का साथ ही असली खुशी है",
            author: "राहुल शर्मा",
            theme: "friendship",
            likes: 145,
            views: 498,
            shareCount: 78,
            isLiked: false
        }
    ],
    socialPlatforms: [
        {name: "WhatsApp", icon: "whatsapp", color: "#25D366"},
        {name: "Facebook", icon: "facebook", color: "#1877F2"}, 
        {name: "Twitter", icon: "twitter", color: "#1DA1F2"},
        {name: "Instagram", icon: "instagram", color: "#E4405F"},
        {name: "Telegram", icon: "telegram", color: "#0088CC"}
    ]
};

// Application state
let appState = {
    shayariData: [...applicationData.shayariCollection],
    activeShareDropdown: null,
    commentsCount: 12
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('Multiple Shayari Post Page loaded, initializing...');
    initializeApplication();
});

function initializeApplication() {
    setupEventListeners();
    setupShayariInteractions();
    setupCommentSystem();
    setupSearchFunctionality();
    setupNavigation();
    setupModals();
    console.log('Application initialization complete');
}

// Setup all event listeners
function setupEventListeners() {
    // Header navigation
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', handleNavigation);
    });

    // Search functionality
    const searchBtn = document.querySelector('.search-btn');
    const searchInput = document.querySelector('.search-input');
    
    if (searchBtn) {
        searchBtn.addEventListener('click', handleSearch);
    }
    
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleSearch();
            }
        });
    }

    // Breadcrumb navigation
    const breadcrumbLinks = document.querySelectorAll('.breadcrumb-link');
    breadcrumbLinks.forEach(link => {
        link.addEventListener('click', handleBreadcrumbClick);
    });

    // Footer links
    const footerLinks = document.querySelectorAll('.footer-links a, .social-link');
    footerLinks.forEach(link => {
        link.addEventListener('click', handleFooterLinks);
    });

    // Sidebar interactions
    const sidebarLinks = document.querySelectorAll('.related-post, .category-badge');
    sidebarLinks.forEach(link => {
        link.addEventListener('click', handleSidebarClick);
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', handleOutsideClick);
}

// Setup shayari-specific interactions
function setupShayariInteractions() {
    const shayariCards = document.querySelectorAll('.shayari-card');
    
    shayariCards.forEach(card => {
        const shayariId = parseInt(card.dataset.id);
        const shayariData = appState.shayariData.find(s => s.id === shayariId);
        
        if (shayariData) {
            setupShayariCard(card, shayariData);
        }
    });
}

function setupShayariCard(cardElement, shayariData) {
    const likeBtn = cardElement.querySelector('.like-btn');
    const shareBtn = cardElement.querySelector('.share-btn');
    const copyBtn = cardElement.querySelector('.copy-btn');
    const downloadBtn = cardElement.querySelector('.download-btn');
    const shareMenu = cardElement.querySelector('.share-menu');
    const shareOptions = cardElement.querySelectorAll('.share-option');

    // Like functionality
    if (likeBtn) {
        likeBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            handleLike(shayariData, likeBtn);
        });
    }

    // Share dropdown functionality
    if (shareBtn && shareMenu) {
        shareBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            handleShareDropdown(shareMenu, shayariData.id);
        });
    }

    // Share options
    shareOptions.forEach(option => {
        option.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            const platform = option.dataset.platform;
            handleShareToPlatform(platform, shayariData);
            closeShareDropdown(shareMenu);
        });
    });

    // Copy functionality
    if (copyBtn) {
        copyBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            handleCopyShayari(shayariData);
        });
    }

    // Download functionality
    if (downloadBtn) {
        downloadBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            handleDownloadShayari(shayariData);
        });
    }
}

// Handle like functionality for individual shayari
function handleLike(shayariData, likeBtn) {
    shayariData.isLiked = !shayariData.isLiked;
    
    if (shayariData.isLiked) {
        shayariData.likes += 1;
        likeBtn.classList.add('liked');
        showToast(`शायरी को पसंद किया गया! ❤️`);
        
        // Add heart animation
        createHeartAnimation(likeBtn);
    } else {
        shayariData.likes -= 1;
        likeBtn.classList.remove('liked');
        showToast('पसंद हटा दी गई');
    }
    
    // Update like count display
    const likeText = likeBtn.querySelector('.btn-text');
    if (likeText) {
        likeText.textContent = shayariData.likes.toString();
    }
}

// Create heart animation effect
function createHeartAnimation(button) {
    const heart = document.createElement('span');
    heart.textContent = '❤️';
    heart.style.cssText = `
        position: absolute;
        pointer-events: none;
        font-size: 20px;
        animation: heartFloat 1s ease-out forwards;
        z-index: 1000;
    `;
    
    // Add CSS animation if not exists
    if (!document.querySelector('#heart-animation-styles')) {
        const style = document.createElement('style');
        style.id = 'heart-animation-styles';
        style.textContent = `
            @keyframes heartFloat {
                0% {
                    transform: translateY(0) scale(1);
                    opacity: 1;
                }
                100% {
                    transform: translateY(-30px) scale(1.5);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
    
    const rect = button.getBoundingClientRect();
    heart.style.left = (rect.left + rect.width / 2) + 'px';
    heart.style.top = rect.top + 'px';
    heart.style.position = 'fixed';
    
    document.body.appendChild(heart);
    
    setTimeout(() => {
        if (heart.parentNode) {
            heart.parentNode.removeChild(heart);
        }
    }, 1000);
}

// Handle share dropdown
function handleShareDropdown(shareMenu, shayariId) {
    // Close any other open dropdowns
    if (appState.activeShareDropdown && appState.activeShareDropdown !== shareMenu) {
        closeShareDropdown(appState.activeShareDropdown);
    }
    
    // Toggle current dropdown
    const isHidden = shareMenu.classList.contains('hidden');
    
    if (isHidden) {
        // Show dropdown
        shareMenu.classList.remove('hidden');
        appState.activeShareDropdown = shareMenu;
    } else {
        // Hide dropdown
        closeShareDropdown(shareMenu);
    }
}

function closeShareDropdown(shareMenu) {
    if (shareMenu) {
        shareMenu.classList.add('hidden');
        if (appState.activeShareDropdown === shareMenu) {
            appState.activeShareDropdown = null;
        }
    }
}

// Handle sharing to specific platforms
function handleShareToPlatform(platform, shayariData) {
    const shareText = `${shayariData.text}\n\n- ${shayariData.author}\n\n#शायरी #हिंदीशायरी`;
    const shareUrl = window.location.href;
    
    switch (platform) {
        case 'whatsapp':
            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(shareText + '\n\n' + shareUrl)}`;
            window.open(whatsappUrl, '_blank');
            showToast('WhatsApp में शेयर करें! 📱');
            break;
            
        case 'facebook':
            const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${encodeURIComponent(shareText)}`;
            window.open(facebookUrl, '_blank');
            showToast('Facebook में शेयर करें! 📘');
            break;
            
        case 'twitter':
            const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`;
            window.open(twitterUrl, '_blank');
            showToast('Twitter में शेयर करें! 🐦');
            break;
            
        case 'instagram':
            // Instagram doesn't support direct text sharing, so copy to clipboard
            copyToClipboard(shareText);
            showToast('टेक्स्ट कॉपी हो गया! Instagram में पेस्ट करें 📷');
            break;
            
        case 'telegram':
            const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(shareUrl)}&text=${encodeURIComponent(shareText)}`;
            window.open(telegramUrl, '_blank');
            showToast('Telegram में शेयर करें! ✈️');
            break;
    }
    
    // Update share count
    shayariData.shareCount += 1;
}

// Handle copy shayari text
function handleCopyShayari(shayariData) {
    const textToCopy = `${shayariData.text}\n\n- ${shayariData.author}`;
    
    copyToClipboard(textToCopy).then(() => {
        showCopySuccessModal();
    }).catch(() => {
        showToast('कॉपी नहीं हो सका');
    });
}

// Copy text to clipboard
function copyToClipboard(text) {
    return new Promise((resolve, reject) => {
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text).then(() => {
                console.log('Text copied to clipboard successfully');
                resolve();
            }).catch((err) => {
                console.log('Clipboard API failed, trying fallback');
                fallbackCopyTextToClipboard(text, resolve, reject);
            });
        } else {
            fallbackCopyTextToClipboard(text, resolve, reject);
        }
    });
}

// Fallback for copying text
function fallbackCopyTextToClipboard(text, resolve, reject) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 2em;
        height: 2em;
        padding: 0;
        border: none;
        outline: none;
        box-shadow: none;
        background: transparent;
        opacity: 0;
        pointer-events: none;
    `;
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);
        
        if (successful) {
            console.log('Text copied using fallback method');
            resolve();
        } else {
            console.error('Fallback copy failed');
            reject();
        }
    } catch (err) {
        console.error('Fallback copy failed:', err);
        document.body.removeChild(textArea);
        reject();
    }
}

// Show copy success modal
function showCopySuccessModal() {
    const modal = document.getElementById('copyModal');
    if (modal) {
        modal.classList.remove('hidden');
        
        // Auto-hide after 2 seconds
        setTimeout(() => {
            modal.classList.add('hidden');
        }, 2000);
    }
}

// Setup modals
function setupModals() {
    const copyModal = document.getElementById('copyModal');
    
    if (copyModal) {
        // Close modal when clicking on backdrop
        const backdrop = copyModal.querySelector('.modal-backdrop');
        if (backdrop) {
            backdrop.addEventListener('click', () => {
                copyModal.classList.add('hidden');
            });
        }
        
        // Close modal when clicking outside content
        copyModal.addEventListener('click', (e) => {
            if (e.target === copyModal) {
                copyModal.classList.add('hidden');
            }
        });
    }
}

// Handle download shayari as image
function handleDownloadShayari(shayariData) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // Set canvas dimensions
    canvas.width = 800;
    canvas.height = 600;
    
    // Set background based on theme
    const themeColors = {
        love: ['#ff6b9d', '#c44569'],
        sad: ['#485563', '#29323c'],
        motivational: ['#ff7675', '#fd79a8'],
        friendship: ['#00b894', '#00cec9']
    };
    
    const colors = themeColors[shayariData.theme] || ['#485563', '#29323c'];
    
    // Create gradient background
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
    gradient.addColorStop(0, colors[0]);
    gradient.addColorStop(1, colors[1]);
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Add overlay
    ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Set text properties
    ctx.fillStyle = 'white';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
    ctx.shadowOffsetX = 2;
    ctx.shadowOffsetY = 2;
    ctx.shadowBlur = 4;
    
    // Add shayari text
    ctx.font = 'bold 24px Arial, sans-serif';
    const lines = shayariData.text.split('\n');
    let y = canvas.height / 2 - (lines.length * 20);
    
    lines.forEach(line => {
        if (line.trim()) {
            ctx.fillText(line, canvas.width / 2, y);
            y += 50;
        } else {
            y += 25;
        }
    });
    
    // Add author
    ctx.font = 'italic 20px Arial, sans-serif';
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.fillText(`- ${shayariData.author}`, canvas.width / 2, y + 40);
    
    // Add watermark
    ctx.font = '16px Arial, sans-serif';
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    ctx.fillText('शायरी ब्लॉग', canvas.width / 2, canvas.height - 40);
    
    // Download the image
    const link = document.createElement('a');
    link.download = `shayari-${shayariData.id}-${Date.now()}.png`;
    link.href = canvas.toDataURL();
    link.click();
    
    showToast('शायरी डाउनलोड हो गई! 📥');
}

// Setup comment system
function setupCommentSystem() {
    const commentForm = document.getElementById('commentForm');
    
    if (commentForm) {
        commentForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const commentInput = this.querySelector('.comment-input');
            const commentText = commentInput.value.trim();
            
            if (commentText) {
                addNewComment(commentText);
                commentInput.value = '';
                showToast('टिप्पणी पोस्ट हो गई! 💬');
            } else {
                showToast('कृपया टिप्पणी लिखें');
            }
        });
    }
    
    // Setup existing comment interactions
    const commentLikeBtns = document.querySelectorAll('.comment-like-btn');
    const commentReplyBtns = document.querySelectorAll('.comment-reply-btn');
    
    commentLikeBtns.forEach(btn => {
        btn.addEventListener('click', handleCommentLike);
    });
    
    commentReplyBtns.forEach(btn => {
        btn.addEventListener('click', handleCommentReply);
    });
}

function addNewComment(text) {
    const commentsList = document.getElementById('commentsList');
    const commentsTitle = document.querySelector('.comments-title');
    
    if (commentsList) {
        const newComment = document.createElement('div');
        newComment.className = 'comment';
        newComment.innerHTML = `
            <div class="comment-avatar"></div>
            <div class="comment-content">
                <div class="comment-header">
                    <h4 class="comment-author">आप</h4>
                    <span class="comment-time">अभी</span>
                </div>
                <p class="comment-text">${text}</p>
                <div class="comment-actions">
                    <button class="comment-like-btn">❤️ 0</button>
                    <button class="comment-reply-btn">जवाब दें</button>
                </div>
            </div>
        `;
        
        // Add event listeners to new comment
        const likeBtn = newComment.querySelector('.comment-like-btn');
        const replyBtn = newComment.querySelector('.comment-reply-btn');
        
        likeBtn.addEventListener('click', handleCommentLike);
        replyBtn.addEventListener('click', handleCommentReply);
        
        // Insert at the beginning
        commentsList.insertBefore(newComment, commentsList.firstChild);
        
        // Update comments count
        appState.commentsCount++;
        commentsTitle.textContent = `टिप्पणियाँ (${appState.commentsCount})`;
    }
}

function handleCommentLike(e) {
    const btn = e.currentTarget;
    const countText = btn.textContent;
    const currentCount = parseInt(countText.match(/\d+/)[0]);
    const newCount = currentCount + 1;
    
    btn.textContent = `❤️ ${newCount}`;
    btn.style.color = 'var(--color-primary)';
    
    showToast('टिप्पणी को पसंद किया! ❤️');
}

function handleCommentReply(e) {
    const comment = e.currentTarget.closest('.comment');
    const author = comment.querySelector('.comment-author').textContent;
    const commentInput = document.querySelector('.comment-input');
    
    if (commentInput) {
        commentInput.value = `@${author} `;
        commentInput.focus();
    }
}

// Setup search functionality
function setupSearchFunctionality() {
    const searchInput = document.querySelector('.search-input');
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase().trim();
            
            if (query.length > 2) {
                // Simple search highlighting (in a real app, this would be more sophisticated)
                highlightSearchTerms(query);
            } else {
                clearSearchHighlights();
            }
        });
    }
}

function highlightSearchTerms(query) {
    const shayariTexts = document.querySelectorAll('.shayari-line');
    
    shayariTexts.forEach(element => {
        const text = element.textContent;
        const regex = new RegExp(`(${query})`, 'gi');
        
        if (regex.test(text)) {
            const highlightedText = text.replace(regex, '<mark>$1</mark>');
            element.innerHTML = highlightedText;
        }
    });
}

function clearSearchHighlights() {
    const highlightedElements = document.querySelectorAll('.shayari-line mark');
    
    highlightedElements.forEach(element => {
        const parent = element.parentNode;
        parent.textContent = parent.textContent;
    });
}

// Setup navigation
function setupNavigation() {
    // Close dropdowns on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            if (appState.activeShareDropdown) {
                closeShareDropdown(appState.activeShareDropdown);
            }
            
            // Close copy modal
            const copyModal = document.getElementById('copyModal');
            if (copyModal && !copyModal.classList.contains('hidden')) {
                copyModal.classList.add('hidden');
            }
        }
    });
}

// Navigation handlers
function handleNavigation(e) {
    e.preventDefault();
    const linkText = this.textContent;
    showToast(`${linkText} पेज जल्द ही आएगा!`);
}

function handleSearch() {
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        const query = searchInput.value.trim();
        if (query) {
            showToast(`"${query}" की खोज की गई`);
        } else {
            showToast('कुछ खोजें');
        }
    }
}

function handleBreadcrumbClick(e) {
    e.preventDefault();
    const linkText = this.textContent;
    showToast(`${linkText} पर जाया जा रहा है...`);
}

function handleFooterLinks(e) {
    e.preventDefault();
    const linkText = this.textContent;
    showToast(`${linkText} सेक्शन जल्द ही आएगा!`);
}

function handleSidebarClick(e) {
    e.preventDefault();
    let title = 'संबंधित पोस्ट';
    const titleElement = this.querySelector('h5') || this;
    
    if (titleElement) {
        title = titleElement.textContent;
    }
    
    showToast(`"${title}" पर जाया जा रहा है...`);
}

function handleOutsideClick(e) {
    // Close share dropdowns when clicking outside
    if (appState.activeShareDropdown) {
        const isClickInsideDropdown = appState.activeShareDropdown.contains(e.target);
        const isClickOnShareButton = e.target.closest('.share-btn');
        
        if (!isClickInsideDropdown && !isClickOnShareButton) {
            closeShareDropdown(appState.activeShareDropdown);
        }
    }
}

// Utility functions
function showToast(message) {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: var(--color-surface);
        color: var(--color-text);
        padding: 12px 20px;
        border-radius: 8px;
        border: 1px solid var(--color-border);
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        font-size: 14px;
        max-width: 300px;
        animation: slideIn 0.3s ease-out forwards;
    `;
    
    // Add CSS animation if not exists
    if (!document.querySelector('#toast-styles')) {
        const style = document.createElement('style');
        style.id = 'toast-styles';
        style.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            @keyframes slideOut {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
    
    document.body.appendChild(toast);
    
    // Remove toast after 3 seconds
    setTimeout(() => {
        toast.style.animation = 'slideOut 0.3s ease-out forwards';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// Smooth scroll for internal navigation
function smoothScrollTo(element) {
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Export functions for global access
window.ShayariApp = {
    appState,
    handleLike,
    handleCopyShayari,
    handleDownloadShayari,
    showToast,
    copyToClipboard
};

console.log('Multiple Shayari Post application loaded successfully');