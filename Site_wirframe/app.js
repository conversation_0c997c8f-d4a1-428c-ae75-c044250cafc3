// Sample data from the provided JSON
const blogData = {
    categories: [
        "प्रेम शायरी",
        "दुख शायरी", 
        "मोटिवेशनल शायरी",
        "दोस्ती शायरी",
        "जिंदगी शायरी",
        "रोमांटिक शायरी"
    ],
    authors: [
        "राहुल शर्मा",
        "प्रिया गुप्ता", 
        "अमित कुमार",
        "सुनीता देवी",
        "विकास पांडे"
    ],
    sampleShayari: [
        {
            title: "दिल की बात",
            author: "राहुल शर्मा",
            category: "प्रेम शायरी",
            excerpt: "जब से तुम मिले हो, जिंदगी में रंग आ गया है...",
            likes: 245,
            date: "15 जनवरी 2025"
        },
        {
            title: "टूटे सपने",
            author: "प्रिया गुप्ता",
            category: "दुख शायरी", 
            excerpt: "कुछ सपने टूटकर भी खुशियाँ दे जाते हैं...",
            likes: 189,
            date: "12 जनवरी 2025"
        },
        {
            title: "हौसलों का जमाना",
            author: "अमित कुमार",
            category: "मोटिवेशनल शायरी",
            excerpt: "हार कर भी जीतने का जुनून रखना...",
            likes: 356,
            date: "10 जनवरी 2025"
        },
        {
            title: "दोस्ती का रिश्ता",
            author: "सुनीता देवी",
            category: "दोस्ती शायरी",
            excerpt: "सच्चे दोस्त वो होते हैं जो मुश्किल वक्त में साथ खड़े रहते हैं...",
            likes: 298,
            date: "8 जनवरी 2025"
        },
        {
            title: "जिंदगी के रंग",
            author: "विकास पांडे",
            category: "जिंदगी शायरी",
            excerpt: "हर दिन नया होता है, हर पल एक नई शुरुआत...",
            likes: 412,
            date: "5 जनवरी 2025"
        },
        {
            title: "प्रेम की गहराई",
            author: "राहुल शर्मा",
            category: "रोमांटिक शायरी",
            excerpt: "तुम्हारी मोहब्बत में डूबकर हमने जीना सीखा है...",
            likes: 523,
            date: "2 जनवरी 2025"
        }
    ]
};

// State management
let currentShayariData = [...blogData.sampleShayari];
let selectedCategory = 'all';
let searchQuery = '';

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing app...');
    initializeApp();
});

function initializeApp() {
    renderShayariCards();
    renderCategories();
    setupEventListeners();
    setupNewsletterSignup();
}

// Render shayari cards
function renderShayariCards() {
    const shayariGrid = document.getElementById('shayariGrid');
    if (!shayariGrid) {
        console.log('Shayari grid not found');
        return;
    }
    
    let filteredData = currentShayariData;
    
    // Apply category filter
    if (selectedCategory !== 'all') {
        filteredData = filteredData.filter(shayari => 
            shayari.category === selectedCategory
        );
    }
    
    // Apply search filter
    if (searchQuery) {
        filteredData = filteredData.filter(shayari => 
            shayari.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            shayari.author.toLowerCase().includes(searchQuery.toLowerCase()) ||
            shayari.excerpt.toLowerCase().includes(searchQuery.toLowerCase())
        );
    }
    
    shayariGrid.innerHTML = '';
    
    filteredData.forEach((shayari, index) => {
        const card = createShayariCard(shayari, index);
        shayariGrid.appendChild(card);
    });
    
    if (filteredData.length === 0) {
        shayariGrid.innerHTML = `
            <div class="no-results" style="text-align: center; padding: 40px; color: var(--color-text-secondary); grid-column: 1 / -1;">
                <h3>कोई शायरी नहीं मिली</h3>
                <p>कृपया अपनी खोज या फ़िल्टर बदलें</p>
            </div>
        `;
    }
    
    console.log(`Rendered ${filteredData.length} cards`);
}

// Create individual shayari card
function createShayariCard(shayari, index) {
    const card = document.createElement('div');
    card.className = 'shayari-card';
    card.innerHTML = `
        <div class="card-image-placeholder"></div>
        <h3 class="card-title">${shayari.title}</h3>
        <p class="card-author">लेखक: ${shayari.author}</p>
        <p class="card-excerpt">${shayari.excerpt}</p>
        <div class="card-meta">
            <span class="card-category">${shayari.category}</span>
            <span class="card-date">${shayari.date}</span>
        </div>
        <div class="card-actions">
            <div class="card-engagement">
                <button class="like-count" onclick="handleLike(${index}, this)">
                    ❤️ <span>${shayari.likes}</span>
                </button>
                <button class="share-btn" onclick="handleShare(${index})">
                    📤 Share
                </button>
            </div>
            <button class="btn btn--sm btn--primary read-full-btn" onclick="handleReadFull(${index})">
                पढ़ें
            </button>
        </div>
    `;
    return card;
}

// Render categories in sidebar
function renderCategories() {
    const categoriesList = document.getElementById('categoriesList');
    if (!categoriesList) {
        console.log('Categories list not found');
        return;
    }
    
    categoriesList.innerHTML = '';
    
    // Add "All Categories" option
    const allLink = document.createElement('a');
    allLink.href = '#';
    allLink.className = `category-link ${selectedCategory === 'all' ? 'active' : ''}`;
    allLink.textContent = 'सभी श्रेणियाँ';
    allLink.onclick = function(e) {
        e.preventDefault();
        filterByCategory('all');
    };
    categoriesList.appendChild(allLink);
    
    // Add individual category links
    blogData.categories.forEach(category => {
        const link = document.createElement('a');
        link.href = '#';
        link.className = `category-link ${selectedCategory === category ? 'active' : ''}`;
        link.textContent = category;
        link.onclick = function(e) {
            e.preventDefault();
            filterByCategory(category);
        };
        categoriesList.appendChild(link);
    });
    
    console.log('Categories rendered');
}

// Filter by category
function filterByCategory(category) {
    console.log('Filtering by category:', category);
    selectedCategory = category;
    renderShayariCards();
    renderCategories(); // Re-render to update active states
}

// Search functionality
function performSearch() {
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchQuery = searchInput.value.trim();
        console.log('Performing search:', searchQuery);
        renderShayariCards();
    }
}

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    const searchBtn = document.querySelector('.search-btn');
    if (searchBtn) {
        searchBtn.onclick = function(e) {
            e.preventDefault();
            performSearch();
        };
    }
    
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.onkeypress = function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        };
        
        // Real-time search (debounced)
        let searchTimeout;
        searchInput.oninput = function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch();
            }, 300);
        };
    }
    
    // Load more functionality
    const loadMoreBtn = document.querySelector('.load-more-container .btn');
    if (loadMoreBtn) {
        loadMoreBtn.onclick = function(e) {
            e.preventDefault();
            loadMoreShayari();
        };
    }
    
    // Language toggle
    const langToggle = document.querySelector('.lang-toggle');
    if (langToggle) {
        langToggle.onclick = function() {
            alert('भाषा बदलने की सुविधा जल्द ही आएगी!');
        };
    }
    
    // Hero read more button
    const heroReadMore = document.querySelector('.hero .btn');
    if (heroReadMore) {
        heroReadMore.onclick = function(e) {
            e.preventDefault();
            alert('Featured शायरी:\n\n"दिल से दिल तक का सफर\nशब्दों में बयाँ करता है\nहर एक शायर अपनी कलम से\nजिंदगी का राज़ बताता है"\n\n- फीचर्ड पोएट');
        };
    }
    
    // Navigation links
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.onclick = function(e) {
            e.preventDefault();
            const text = this.textContent;
            alert(`${text} पेज जल्द ही आएगा!`);
        };
    });
    
    // Footer links
    const footerLinks = document.querySelectorAll('.footer-links a, .social-link');
    footerLinks.forEach(link => {
        link.onclick = function(e) {
            e.preventDefault();
            const text = this.textContent;
            alert(`${text} सेक्शन जल्द ही आएगा!`);
        };
    });
    
    console.log('Event listeners setup complete');
}

// Handle like functionality
function handleLike(index, btn) {
    console.log('Like clicked for index:', index);
    currentShayariData[index].likes += 1;
    const likeSpan = btn.querySelector('span');
    if (likeSpan) {
        likeSpan.textContent = currentShayariData[index].likes;
    }
    
    // Add visual feedback
    btn.style.color = 'var(--color-primary)';
    setTimeout(() => {
        btn.style.color = '';
    }, 500);
    
    alert(`आपने "${currentShayariData[index].title}" को पसंद किया! कुल लाइक्स: ${currentShayariData[index].likes}`);
}

// Handle share functionality
function handleShare(index) {
    console.log('Share clicked for index:', index);
    const shayari = currentShayariData[index];
    const shareText = `${shayari.title} - ${shayari.author}\n\n${shayari.excerpt}\n\n#शायरी #हिंदीशायरी`;
    
    if (navigator.share) {
        navigator.share({
            title: shayari.title,
            text: shareText,
            url: window.location.href
        }).catch(err => console.log('Error sharing:', err));
    } else {
        // Fallback for browsers that don't support Web Share API
        if (navigator.clipboard) {
            navigator.clipboard.writeText(shareText).then(() => {
                alert('शायरी कॉपी हो गई है!');
            }).catch(() => {
                alert(`शायरी शेयर करें:\n\n${shareText}`);
            });
        } else {
            alert(`शायरी शेयर करें:\n\n${shareText}`);
        }
    }
}

// Handle read full functionality
function handleReadFull(index) {
    console.log('Read full clicked for index:', index);
    const shayari = currentShayariData[index];
    alert(`${shayari.title}\n\nलेखक: ${shayari.author}\nश्रेणी: ${shayari.category}\nदिनांक: ${shayari.date}\n\n${shayari.excerpt}\n\n[पूर्ण शायरी यहाँ दिखाई जाएगी]\n\nयह एक वायरफ्रेम है - वास्तविक शायरी व्यू जल्द ही आएगा!`);
}

// Load more shayari (simulated)
function loadMoreShayari() {
    console.log('Loading more shayari...');
    const loadMoreBtn = document.querySelector('.load-more-container .btn');
    
    // Simulate loading more content
    const additionalShayari = [
        {
            title: "खुशियों का मंजर",
            author: "सुनीता देवी",
            category: "जिंदगी शायरी",
            excerpt: "हर दिन में छुपी होती है एक नई खुशी...",
            likes: 167,
            date: "28 जनवरी 2025"
        },
        {
            title: "यादों का सफर",
            author: "विकास पांडे",
            category: "दुख शायरी",
            excerpt: "कुछ यादें दिल में बस जाती हैं और फिर कभी नहीं जातीं...",
            likes: 234,
            date: "25 जनवरी 2025"
        },
        {
            title: "सपनों की उड़ान",
            author: "प्रिया गुप्ता",
            category: "मोटिवेशनल शायरी",
            excerpt: "सपने वो नहीं जो सोते वक्त आते हैं, सपने वो हैं जो सोने नहीं देते...",
            likes: 445,
            date: "22 जनवरी 2025"
        }
    ];
    
    // Add loading state
    if (loadMoreBtn) {
        loadMoreBtn.textContent = 'Loading...';
        loadMoreBtn.disabled = true;
        
        setTimeout(() => {
            currentShayariData.push(...additionalShayari);
            renderShayariCards();
            loadMoreBtn.textContent = 'Load More शायरी';
            loadMoreBtn.disabled = false;
            alert('नई शायरियाँ लोड हो गईं!');
        }, 1000);
    }
}

// Newsletter signup functionality
function setupNewsletterSignup() {
    const newsletterForm = document.querySelector('.newsletter-signup');
    if (newsletterForm) {
        const submitBtn = newsletterForm.querySelector('.btn');
        const emailInput = newsletterForm.querySelector('input[type="email"]');
        
        if (submitBtn && emailInput) {
            submitBtn.onclick = function(e) {
                e.preventDefault();
                const email = emailInput.value.trim();
                
                if (email && isValidEmail(email)) {
                    alert('धन्यवाद! आपका सब्स्क्रिप्शन सफल रहा।\n\nअब आपको रोज़ाना नई शायरी मिलती रहेगी।');
                    emailInput.value = '';
                } else {
                    alert('कृपया वैध ईमेल पता दर्ज करें।');
                }
            };
        }
    }
}

// Email validation helper
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Export functions for global access
window.handleLike = handleLike;
window.handleShare = handleShare;
window.handleReadFull = handleReadFull;
window.filterByCategory = filterByCategory;
window.performSearch = performSearch;
window.loadMoreShayari = loadMoreShayari;

// Export data for potential external use
window.ShayariBlog = {
    blogData,
    currentShayariData,
    selectedCategory,
    searchQuery
};

console.log('Shayari blog JavaScript loaded successfully');