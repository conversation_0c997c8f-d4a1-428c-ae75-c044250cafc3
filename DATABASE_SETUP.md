# Blog Website Database Setup & Migration Guide

This guide will help you set up and manage the database for your blog website using Supabase.

## 🚀 Quick Start

### 1. Project Information
- **Supabase Project ID**: `cgmlpbxwmqynmshecaqn`
- **Project URL**: `https://cgmlpbxwmqynmshecaqn.supabase.co`
- **Organization**: WORDPRESS2

### 2. Environment Setup

1. Copy the environment template:
   ```bash
   cp .env.example .env.local
   ```

2. The `.env.local` file is already configured with your project details:
   - Project URL
   - API Keys (anon and service_role)
   - Database connection string

### 3. Install Dependencies

```bash
npm install @supabase/supabase-js dotenv
```

### 4. Run Database Migrations

```bash
# Run all pending migrations
node scripts/migrate.js up
```

## 📊 Database Schema

The blog website includes the following tables:

### Core Tables

1. **users** - User profiles (extends auth.users)
   - id, email, full_name, username, avatar_url, bio, role
   - Roles: admin, editor, user

2. **categories** - Blog post categories
   - id, name, slug, description, color
   - Pre-populated with: Technology, Lifestyle, Travel, Food, Business

3. **posts** - Blog articles
   - id, title, slug, excerpt, content, featured_image
   - status (draft, published, archived)
   - author_id, category_id, tags, meta fields
   - view_count, reading_time, published_at

4. **comments** - User comments on posts
   - id, post_id, author_id, parent_id (for replies)
   - content, author_name, author_email
   - status (pending, approved, rejected, spam)

5. **likes** - Post likes/reactions
   - id, post_id, user_id
   - Unique constraint on (post_id, user_id)

6. **bookmarks** - User bookmarks
   - id, post_id, user_id
   - Unique constraint on (post_id, user_id)

## 🔒 Security Features

### Row Level Security (RLS)
All tables have RLS enabled with appropriate policies:

- **Users**: Can view all profiles, update own profile
- **Categories**: Public read, admin-only write
- **Posts**: Public read for published, author/admin write
- **Comments**: Public read for approved, authenticated write
- **Likes/Bookmarks**: Users manage their own

### User Roles
- **admin**: Full access to all content and users
- **editor**: Can manage posts and comments
- **user**: Can create posts, comments, likes, bookmarks

## 🛠 Database Functions

### Available Functions

1. **handle_new_user()** - Automatically creates user profile on signup
2. **increment_post_views(post_uuid)** - Increments post view count
3. **get_post_with_details(post_slug)** - Gets post with author/category info
4. **get_popular_posts(limit_count)** - Gets most viewed/liked posts
5. **search_posts(search_term, limit_count)** - Full-text search

### Usage Examples

```sql
-- Get a post with all details
SELECT * FROM get_post_with_details('my-first-blog-post');

-- Get top 5 popular posts
SELECT * FROM get_popular_posts(5);

-- Search for posts
SELECT * FROM search_posts('javascript tutorial', 10);

-- Increment view count
SELECT increment_post_views('post-uuid-here');
```

## 📁 Migration Files

1. **001_initial_schema.sql** - Creates all tables and indexes
2. **002_rls_policies.sql** - Sets up Row Level Security policies
3. **003_functions.sql** - Creates utility functions and triggers

## 🔧 Migration Commands

```bash
# Run all pending migrations
node scripts/migrate.js up

# Reset database (WARNING: Destructive)
node scripts/migrate.js reset

# View available commands
node scripts/migrate.js
```

## 🌐 Supabase Dashboard

Access your project dashboard at:
https://supabase.com/dashboard/project/cgmlpbxwmqynmshecaqn

From the dashboard you can:
- View and edit data in the Table Editor
- Monitor real-time subscriptions
- Manage authentication settings
- Configure storage buckets
- View API documentation

## 📝 Sample Data

To add sample data for testing:

```sql
-- Insert a test user (after authentication)
INSERT INTO users (id, email, full_name, username, role) 
VALUES (auth.uid(), '<EMAIL>', 'Admin User', 'admin', 'admin');

-- Insert a sample post
INSERT INTO posts (title, slug, excerpt, content, status, author_id, category_id)
VALUES (
  'Welcome to Our Blog',
  'welcome-to-our-blog',
  'This is our first blog post!',
  'Welcome to our amazing blog. We will be sharing great content here.',
  'published',
  auth.uid(),
  (SELECT id FROM categories WHERE slug = 'technology' LIMIT 1)
);
```

## 🚨 Important Notes

1. **Never commit .env.local** - It contains sensitive API keys
2. **Service Role Key** - Only use server-side, never in client code
3. **Database Password** - Use the password you set during project creation: `BlogWebsite2024!`
4. **Backup Strategy** - Set up regular backups in Supabase dashboard

## 🆘 Troubleshooting

### Common Issues

1. **Migration fails**: Check your .env.local file has correct values
2. **Permission denied**: Ensure you're using the service_role key for migrations
3. **Connection timeout**: Check your internet connection and Supabase status

### Getting Help

- Supabase Documentation: https://supabase.com/docs
- Supabase Discord: https://discord.supabase.com
- GitHub Issues: Create issues in your project repository

## ✅ Next Steps

After setting up the database:

1. Set up authentication in your Next.js app
2. Create API routes for CRUD operations
3. Build the blog frontend components
4. Implement file upload for images
5. Add real-time features with Supabase subscriptions

Your database is now ready for your blog website! 🎉
