#!/usr/bin/env node

/**
 * Database Migration Runner for Blog Website
 * 
 * This script helps you run database migrations against your Supabase project.
 * 
 * Usage:
 *   node scripts/migrate.js up    # Run all pending migrations
 *   node scripts/migrate.js down  # Rollback last migration
 *   node scripts/migrate.js reset # Reset database (WARNING: Destructive)
 */

const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env.local file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Migration tracking table
const MIGRATIONS_TABLE = 'schema_migrations';

async function createMigrationsTable() {
  const { error } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS ${MIGRATIONS_TABLE} (
        id SERIAL PRIMARY KEY,
        filename TEXT NOT NULL UNIQUE,
        executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
  });

  if (error) {
    console.error('❌ Failed to create migrations table:', error.message);
    return false;
  }
  return true;
}

async function getExecutedMigrations() {
  const { data, error } = await supabase
    .from(MIGRATIONS_TABLE)
    .select('filename')
    .order('filename');

  if (error) {
    console.error('❌ Failed to get executed migrations:', error.message);
    return [];
  }

  return data.map(row => row.filename);
}

async function executeMigration(filename, sql) {
  try {
    // Execute the migration SQL
    const { error: sqlError } = await supabase.rpc('exec_sql', { sql });
    
    if (sqlError) {
      throw new Error(`SQL execution failed: ${sqlError.message}`);
    }

    // Record the migration as executed
    const { error: recordError } = await supabase
      .from(MIGRATIONS_TABLE)
      .insert({ filename });

    if (recordError) {
      throw new Error(`Failed to record migration: ${recordError.message}`);
    }

    console.log(`✅ Executed migration: ${filename}`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to execute migration ${filename}:`, error.message);
    return false;
  }
}

async function runMigrations() {
  console.log('🚀 Starting database migrations...\n');

  // Create migrations table if it doesn't exist
  if (!(await createMigrationsTable())) {
    return;
  }

  // Get list of executed migrations
  const executedMigrations = await getExecutedMigrations();
  console.log(`📋 Found ${executedMigrations.length} executed migrations`);

  // Get list of migration files
  const migrationsDir = path.join(__dirname, '..', 'supabase', 'migrations');
  
  if (!fs.existsSync(migrationsDir)) {
    console.error('❌ Migrations directory not found:', migrationsDir);
    return;
  }

  const migrationFiles = fs.readdirSync(migrationsDir)
    .filter(file => file.endsWith('.sql'))
    .sort();

  console.log(`📁 Found ${migrationFiles.length} migration files\n`);

  // Execute pending migrations
  let executedCount = 0;
  for (const filename of migrationFiles) {
    if (executedMigrations.includes(filename)) {
      console.log(`⏭️  Skipping already executed: ${filename}`);
      continue;
    }

    const filePath = path.join(migrationsDir, filename);
    const sql = fs.readFileSync(filePath, 'utf8');

    console.log(`🔄 Executing migration: ${filename}`);
    
    if (await executeMigration(filename, sql)) {
      executedCount++;
    } else {
      console.error('❌ Migration failed. Stopping execution.');
      break;
    }
  }

  console.log(`\n✨ Migration complete! Executed ${executedCount} new migrations.`);
}

async function resetDatabase() {
  console.log('⚠️  WARNING: This will reset your entire database!');
  console.log('⚠️  All data will be lost. This action cannot be undone.');
  
  // In a real implementation, you might want to add confirmation prompt
  console.log('🔄 Resetting database...');

  // Drop all tables and recreate schema
  const resetSQL = `
    -- Drop all tables in public schema
    DROP SCHEMA IF EXISTS public CASCADE;
    CREATE SCHEMA public;
    GRANT ALL ON SCHEMA public TO postgres;
    GRANT ALL ON SCHEMA public TO public;
  `;

  const { error } = await supabase.rpc('exec_sql', { sql: resetSQL });
  
  if (error) {
    console.error('❌ Failed to reset database:', error.message);
    return;
  }

  console.log('✅ Database reset complete. Run migrations to recreate schema.');
}

// Main execution
async function main() {
  const command = process.argv[2];

  switch (command) {
    case 'up':
      await runMigrations();
      break;
    case 'reset':
      await resetDatabase();
      break;
    default:
      console.log(`
📚 Database Migration Tool

Usage:
  node scripts/migrate.js up     # Run all pending migrations
  node scripts/migrate.js reset  # Reset database (WARNING: Destructive)

Available migrations:
${fs.readdirSync(path.join(__dirname, '..', 'supabase', 'migrations'))
  .filter(f => f.endsWith('.sql'))
  .map(f => `  - ${f}`)
  .join('\n')}
      `);
  }
}

main().catch(console.error);
